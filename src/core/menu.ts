// @flow
import { app, Menu, shell } from 'electron'
import { MenuItemConstructorOptions } from 'electron/main'

export default class MenuBuilder {
	mainWindow: any
	constructor(mainWindow: any) {
		this.mainWindow = mainWindow
	}

	buildMenu() {
		if (process.env.NODE_ENV === 'development' || process.env.DEBUG_PROD === 'true') {
			this.setupDevelopmentEnvironment()
		}

		const template: MenuItemConstructorOptions[] =
			process.platform === 'darwin' ? this.buildDarwinTemplate() : this.buildDefaultTemplate()

		const menu = Menu.buildFromTemplate(template)
		Menu.setApplicationMenu(menu)

		return menu
	}

	setupDevelopmentEnvironment() {
		this.mainWindow.openDevTools()
		this.mainWindow.webContents.on('context-menu', (_e: any, props: any) => {
			const { x, y } = props

			Menu.buildFromTemplate([
				{
					label: '检查元素',
					click: () => {
						this.mainWindow.inspectElement(x, y)
					}
				}
			]).popup(this.mainWindow)
		})
	}

	buildDarwinTemplate(): MenuItemConstructorOptions[] {
		const subMenuAbout: MenuItemConstructorOptions = {
			label: '豆神王者Club',
			submenu: [
				{
					label: '版本信息',
					click: () => {
						// You can show an about dialog here if needed
					}
				},
				{ type: 'separator' },
				{
					label: '退出',
					accelerator: 'Command+Q',
					click: () => {
						app.quit()
					}
				}
			]
		}
		const subMenuEdit: MenuItemConstructorOptions = {
			label: '编辑',
			submenu: [
				{ label: '剪切', accelerator: 'Command+X', role: 'cut' },
				{ label: '复制', accelerator: 'Command+C', role: 'copy' },
				{ label: '粘贴', accelerator: 'Command+V', role: 'paste' },
				{
					label: '全选',
					accelerator: 'Command+A',
					role: 'selectAll'
				}
			]
		}
		const subMenuViewDev: MenuItemConstructorOptions = {
			label: '显示',
			submenu: [
				{
					label: '刷新',
					accelerator: 'Command+R',
					click: () => {
						this.mainWindow.webContents.reload()
					}
				},
				{
					label: '切换全屏',
					accelerator: 'Ctrl+Command+F',
					click: () => {
						this.mainWindow.setFullScreen(!this.mainWindow.isFullScreen())
					}
				},
				{
					label: '切换开发者工具',
					accelerator: 'Alt+Command+I',
					click: () => {
						this.mainWindow.toggleDevTools()
					}
				}
			]
		}
		const subMenuViewProd: MenuItemConstructorOptions = {
			label: '显示',
			submenu: [
				{
					label: '切换全屏',
					accelerator: 'Ctrl+Command+F',
					click: () => {
						this.mainWindow.setFullScreen(!this.mainWindow.isFullScreen())
					}
				},
				{
					label: '切换开发者工具',
					accelerator: 'Alt+Command+I',
					click: () => {
						this.mainWindow.toggleDevTools()
					}
				}
			]
		}
		const subMenuHelp: MenuItemConstructorOptions = {
			label: '帮助',
			submenu: [
				{
					label: '关于豆神王者Club',
					click() {
						shell.openExternal('https://zmexing.com')
					}
				}
			]
		}

		const subMenuView: MenuItemConstructorOptions =
			process.env.NODE_ENV === 'development' ? subMenuViewDev : subMenuViewProd

		return [subMenuAbout, subMenuEdit, subMenuView, subMenuHelp]
	}

	buildDefaultTemplate(): MenuItemConstructorOptions[] {
		const templateDefault: MenuItemConstructorOptions[] = [
			{
				label: '&View',
				submenu:
					process.env.NODE_ENV === 'development'
						? [
								{
									label: '刷新',
									accelerator: 'Ctrl+R',
									click: () => {
										this.mainWindow.webContents.reload()
									}
								},
								{
									label: '切换全屏',
									accelerator: 'F11',
									click: () => {
										this.mainWindow.setFullScreen(
											!this.mainWindow.isFullScreen()
										)
									}
								},
								{
									label: '切换开发者工具',
									accelerator: 'Alt+Ctrl+I',
									click: () => {
										this.mainWindow.toggleDevTools()
									}
								}
							]
						: [
								{
									label: '切换全屏',
									accelerator: 'F11',
									click: () => {
										this.mainWindow.setFullScreen(
											!this.mainWindow.isFullScreen()
										)
									}
								}
							]
			},
			{
				label: '帮助',
				submenu: [
					{
						label: '关于我们',
						click() {
							shell.openExternal('https://zmexing.com')
						}
					}
				]
			}
		]

		return templateDefault
	}
}
