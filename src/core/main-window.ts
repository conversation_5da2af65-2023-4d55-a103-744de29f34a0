import { BrowserWindow, protocol, screen } from 'electron'
import { BaseWindow, WindowLoadOptions } from './base-window'
import { WindowFactoryOptions, WindowSizeData } from '../method.types'
import { DEBUG } from '../env'
import { ASSETS_PATH, MAIN_WINDOW_SIZE, PROXY, WINDOW_ADAPTER } from './electron-main'
import path, { join } from 'path'
import mime from 'mime-types'
import fs from 'fs-extra'
import serveStatic from 'serve-static'
import finalhandler from 'finalhandler'
import http from 'http'

/**
 * 主窗口类 - 负责创建和管理主应用窗口
 *
 * 继承自BaseWindow，复用通用窗口管理功能。
 * 主要特性：
 * - 支持动态窗口尺寸计算
 * - 支持唯一窗口模式
 * - 支持远程URL加载
 * - 自动设置用户代理
 */
export class MainWindow extends BaseWindow {
	private pack: string
	private windowData: WindowSizeData
	private unique: boolean

	constructor(options: WindowFactoryOptions) {
		const { pack, data = {}, unique = false } = options

		// 计算窗口尺寸
		const { width, height } = data.size || MAIN_WINDOW_SIZE
		const screenSize = screen.getPrimaryDisplay().size
		let ratio = Math.min(screenSize.width / width, screenSize.height / height)
		ratio *= data.ratio || 1

		const finalWidth = (width * ratio) | 0
		const finalHeight = (height * ratio) | 0

		// 调用基类构造函数
		super(`${pack}-${Date.now()}`, {
			width: finalWidth,
			height: finalHeight,
			resizable: true,
			center: true,
			frame: !data.noFrame,
			transparent: !!data.transparent,
			autoHideMenuBar: true,
			title: '豆神王者Club'
		})
		this.handleProtocol()
		this.pack = pack
		this.windowData = data
		this.unique = unique
	}

	/**
	 * 实现抽象方法：获取窗口配置
	 */
	protected getWindowConfig(): Electron.BrowserWindowConstructorOptions {
		return {
			title: this.config.title,
			width: this.config.width,
			height: this.config.height,
			resizable: this.config.resizable,
			center: this.config.center,
			frame: this.config.frame,
			transparent: this.config.transparent,
			autoHideMenuBar: this.config.autoHideMenuBar,
			webPreferences: {
				webSecurity: false,
				nodeIntegration: true,
				contextIsolation: false,
				preload: join(__dirname, '../preload/', 'index.js'),
				...this.config.webPreferences
			}
		}
	}

	/**
	 * 实现抽象方法：获取加载选项
	 */
	protected getLoadOptions(): WindowLoadOptions {
		// 确定加载的URL
		let url: string

		if (DEBUG && WINDOW_ADAPTER[this.pack]) {
			url = WINDOW_ADAPTER[this.pack]
		} else {
			url = `${PROXY}://${this.pack}${DEBUG ? '?env=test' : ''}`
		}

		if (this.windowData.remoteUrl) {
			url = this.windowData.remoteUrl
		}

		return { url }
	}

	/**
	 * 创建窗口前的预处理
	 */
	public create(): BrowserWindow {
		// 处理唯一窗口逻辑
		if (this.unique) {
			this.handleUniqueWindow()
		}

		// 调用基类的create方法
		const window = super.create()

		// 设置窗口标识
		;(window as any).$$name$$ = this.pack

		// 设置用户代理
		this.setUserAgent(this.pack)

		return window
	}

	/**
	 * 处理唯一窗口逻辑
	 */
	private handleUniqueWindow(): void {
		BrowserWindow.getAllWindows().forEach((win: any) => {
			if (win.$$name$$ === this.pack) {
				win.close()
			}
		})
	}

	/**
	 * 获取窗口包名
	 */
	public getPackName(): string {
		return this.pack
	}

	/**
	 * 获取窗口数据
	 */
	public getWindowData(): WindowSizeData {
		return this.windowData
	}

	private handleProtocol(): void {
		protocol.handle('myapp', (request: GlobalRequest): Response => {
			console.log('main-window handleProtocol', request)
			const location = new URL(request.url)
			console.log(location)

			if (location.pathname == '/' || !location.pathname) {
				location.pathname = 'index.html'
			}
			//////测试代码开始
			const resp = new Response()

			staticHandler(request, resp)






			///////  测试代码结束


			//
			// // const file = path.join(ASSETS_PATH, location.pathname, location.pathname)
			// const file = path.join(__dirname, '../../app-assets', location.host, location.pathname)
			// console.log('mainWindow handleProtocol file:', file)
			//
			// try {
			// 	const fileBuffer = fs.readFileSync(file, 'binary')
			// 	const mimeType = mime.lookup(file)
			//
			// 	return new Response(fileBuffer, {
			// 		headers: {
			// 			'content-type':
			// 				typeof mimeType === 'string' ? mimeType : 'application/octet-stream'
			// 		}
			// 	})
			// } catch (error) {
			// 	console.error('read file fail:', error)
			// 	return new Response('<h1>404</h1>', {
			// 		headers: { 'content-type': 'text/html' }
			// 	})
			// }
		})
	}
}
// interface RequestHandler<R extends http.ServerResponse> {
// 	(request: http.IncomingMessage, response: R, next: (err?: HttpError) => void): any;
// }

const createStaticHandler = (rootPath: string, options: serveStatic.ServeStaticOptions = {}) => {
	const serve = serveStatic(rootPath, options)

	return function (req: http.IncomingMessage, res: http.ServerResponse) {
		serve(req, res, finalhandler(req, res))
	}
}

const staticHandler = createStaticHandler('./public', {
	extensions: ['html', 'htm', 'js', 'css'],
	maxAge: '1d'
})
