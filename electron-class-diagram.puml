@startuml ElectronClassDiagram

title Electron应用类关系图 - 从electron-client入口开始

package "Main Process" as MainProcess {

    class MainEntry {
        +app: ElectronApp
        +electronMain: ElectronMain
        --
        +whenReady()
        +onActivate()
        +onWindowAllClosed()
        +setupIPC()
        +broadcastAppReady()
    }

    class ElectronMain {
        -apiHandlers: Map
        -settings: AppSettings
        -customWindows: Map
        -activeWindow: BrowserWindow
        -updaterWindow: UpdaterWindow
        -updateTask: Map
        -errorPage: string
        --
        +constructor()
        +setupIPC()
        +registerAPI()
        +registerDefaultHandlers()
        +broadcast()
        +onRendererEvent()
        +openMainWindow()
        +createUpdaterWindow()
        +closeUpdaterWindow()
        +startDownloadTask()
        +abortDownloadTask()
        +closeCustomWindow()
        +isUpdateAvailable()
        +getSystemInfo()
        +getAppVersion()
        +minimizeWindow()
        +maximizeWindow()
        +closeWindow()
    }

    abstract class BaseWindow {
        #window: BrowserWindow
        #windowId: string
        #config: BaseWindowConfig
        #ipcChannels: Set
        --
        +constructor()
        +create()
        +show()
        +hide()
        +focus()
        +close()
        +isDestroyed()
        +openDevTools()
        +sendMessage()
        #setupEventListeners()
        #setupMenu()
        #loadContent()
        #handleDevelopmentMode()
        #handleWindowClosed()
        #registerIPCChannel()
        #setUserAgent()
        --
        {abstract} #getWindowConfig()
        {abstract} #getLoadOptions()
    }

    class MainWindow {
        -pack: string
        -windowData: WindowSizeData
        -unique: boolean
        --
        +constructor()
        +getPackName()
        +getWindowData()
        --
        #getWindowConfig()
        #getLoadOptions()
        #setupWindowSpecificIPC()
        -handleUniqueWindow()
    }

    class UpdaterWindow {
        -dirname: string
        --
        +constructor()
        +start()
        --
        #getWindowConfig()
        #getLoadOptions()
        #setupWindowSpecificIPC()
    }

    class Downloader {
        -packageName: string
        -cachePath: string
        -url: string
        -md5: string
        -downloadTask: CancelableRequest
        +taskId: string
        --
        +constructor()
        +start()
        +cancel()
        -checkMD5()
        -bindListener()
    }

    class MenuBuilder {
        +mainWindow: BrowserWindow
        --
        +constructor()
        +buildMenu()
        +setupDevelopmentEnvironment()
        +buildDarwinTemplate()
        +buildDefaultTemplate()
    }

    class BrowserWindow {
        +webContents: WebContents
        --
        +loadURL()
        +loadFile()
        +close()
        +show()
        +hide()
        +focus()
        +isDestroyed()
        +minimize()
        +maximize()
        +unmaximize()
        +isMaximized()
        +setFullScreen()
        +isFullScreen()
    }
}

package "Preload Process" as PreloadProcess {

    class PreloadScript {
        +electronBridge: ElectronAPI
        +api: object
        --
        +createElectronBridge()
        +exposeInMainWorld()
        -setupEventListeners()
        -handleIPCEvents()
    }

    class PreloadBridge {
        -eventListeners: Map
        --
        +invoke()
        +on()
        +off()
        +emit()
        +once()
        -handleElectronEventBroadcast()
        -handleDownloadEvent()
    }
}

package "Renderer Process" as RendererProcess {

    class VueApp {
        +systemInfo: Ref
        +appVersion: Ref
        +settings: Ref
        +notifications: Ref
        +isLoading: Ref
        +error: Ref
        +tips: Ref
        +electronClient: ElectronClient
        --
        +constructor()
        +getNovaVersion()
        +doUpdateBundle()
        +updateComplete()
        +loadSystemInfo()
        +loadSettings()
        +setStatus()
        +onMounted()
        +onUnmounted()
        -setupWindowControls()
        -setupEventListeners()
    }

    class ElectronClient {
        -api: ElectronAPI
        -eventUnsubscribers: Map
        --
        +constructor()
        +invoke()
        +onMainProcess()
        +emitToMainProcess()
        +startDownloadTask()
        +openMainWindow()
        +isUpdateAvailable()
        +abortDownloadTask()
        +getSystemInfo()
        +getAppVersion()
        +minimizeWindow()
        +maximizeWindow()
        +closeWindow()
        +cleanup()
        +onThemeChange()
        +onNotification()
        +emitUserAction()
        +emitPageLoaded()
        +emitError()
        -downloadListener()
    }

    class DownloadTask {
        +taskId: string
        +client: ElectronClient
        --
        +constructor()
        +onProgress()
        +onComplete()
        +onError()
        +cancel()
        -unbindListeners()
    }

    class VueMain {
        --
        +createApp()
        +mount()
    }
}

interface ElectronAPI {
    +invoke()
    +on()
    +off()
    +emit()
    +once()
}

interface MainProcessAPI {
    +getSystemInfo()
    +getAppVersion()
    +minimizeWindow()
    +maximizeWindow()
    +closeWindow()
    +getSettings()
    +updateSettings()
    +openMainWindow()
    +closeCustomWindow()
    +startDownloadTask()
    +abortDownloadTask()
    +isUpdateAvailable()
    +openDevTools()
}

class EventEmitter {
    +on()
    +emit()
    +removeAllListeners()
    +once()
    +off()
    +listenerCount()
    +eventNames()
}

' 继承关系
ElectronMain --|> EventEmitter
ElectronClient --|> EventEmitter
BaseWindow --|> EventEmitter
Downloader --|> EventEmitter
MainWindow --|> BaseWindow
UpdaterWindow --|> BaseWindow

' 实现关系
PreloadBridge ..|> ElectronAPI

' 组合关系
MainEntry *-- ElectronMain
ElectronMain *-- MainWindow
ElectronMain *-- UpdaterWindow
ElectronMain *-- Downloader
BaseWindow *-- BrowserWindow
BaseWindow *-- MenuBuilder
ElectronClient *-- DownloadTask
VueApp *-- ElectronClient

' 依赖关系
VueApp ..> MainProcessAPI
ElectronClient ..> MainProcessAPI
PreloadScript ..> ElectronMain
PreloadBridge ..> ElectronMain
DownloadTask ..> ElectronClient

' 入口点标记
note top of MainEntry : 主进程入口点\n(main/index.ts)
note top of VueApp : 渲染进程入口点\n(App.vue)
note top of PreloadScript : 预加载脚本入口点\n(preload/index.ts)

@enduml
