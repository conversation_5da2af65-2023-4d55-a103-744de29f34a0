@startuml ElectronClassDiagram

title Electron应用类关系图 - 从electron-client入口开始

package "Main Process" {

    class MainEntry {
        +app: ElectronApp
        +electronMain: ElectronMain
        --
        +whenReady()
        +onActivate()
        +onWindowAllClosed()
    }

    class ElectronMain {
        -apiHandlers: Map
        -settings: AppSettings
        -customWindows: Map
        -activeWindow: BrowserWindow
        -updaterWindow: UpdaterWindow
        -updateTask: Map
        --
        +constructor()
        +setupIPC()
        +registerAPI()
        +broadcast()
        +onRendererEvent()
        +openMainWindow()
        +createUpdaterWindow()
        +startDownloadTask()
        +isUpdateAvailable()
        +getSystemInfo()
    }

    abstract class BaseWindow {
        #window: BrowserWindow
        #windowId: string
        #config: BaseWindowConfig
        --
        +constructor()
        +create()
        +show()
        +hide()
        +close()
        +isDestroyed()
        --
        {abstract} #getWindowConfig()
        {abstract} #getLoadOptions()
    }

    class MainWindow {
        -pack: string
        -windowData: WindowSizeData
        -unique: boolean
        --
        +constructor()
        +getPackName()
        +getWindowData()
    }

    class UpdaterWindow {
        -dirname: string
        --
        +constructor()
        +start()
    }

    class Downloader {
        -packageName: string
        -cachePath: string
        -url: string
        -md5: string
        +taskId: string
        --
        +constructor()
        +start()
        +cancel()
    }

    class MenuBuilder {
        +mainWindow: BrowserWindow
        --
        +constructor()
        +buildMenu()
    }

    class BrowserWindow {
        +webContents: WebContents
        --
        +loadURL()
        +close()
        +show()
        +hide()
        +isDestroyed()
    }
}

package "Preload Process" {
    
    class PreloadScript {
        +electronBridge: ElectronAPI
        --
        +createElectronBridge()
        +exposeInMainWorld()
    }
    
    class PreloadBridge {
        -eventListeners: Map
        --
        +invoke()
        +on()
        +off()
        +emit()
        +once()
    }
}

package "Renderer Process" {
    
    class VueApp {
        +systemInfo: Ref
        +appVersion: Ref
        +settings: Ref
        +electronClient: ElectronClient
        --
        +getNovaVersion()
        +doUpdateBundle()
        +updateComplete()
        +loadSystemInfo()
        +loadSettings()
    }
    
    class ElectronClient {
        -api: ElectronAPI
        -eventUnsubscribers: Map
        --
        +constructor()
        +invoke()
        +onMainProcess()
        +emitToMainProcess()
        +startDownloadTask()
        +openMainWindow()
        +isUpdateAvailable()
        +getSystemInfo()
        +cleanup()
    }
    
    class DownloadTask {
        +taskId: string
        +client: ElectronClient
        --
        +constructor()
        +onProgress()
        +onComplete()
        +onError()
        +cancel()
    }
}

interface ElectronAPI {
    +invoke()
    +on()
    +off()
    +emit()
    +once()
}

interface MainProcessAPI {
    +getSystemInfo()
    +getAppVersion()
    +openMainWindow()
    +startDownloadTask()
    +isUpdateAvailable()
}

class EventEmitter {
    +on()
    +emit()
    +removeAllListeners()
    +once()
}

' 继承关系
ElectronMain --|> EventEmitter
ElectronClient --|> EventEmitter
BaseWindow --|> EventEmitter
Downloader --|> EventEmitter
MainWindow --|> BaseWindow
UpdaterWindow --|> BaseWindow

' 实现关系
PreloadBridge ..|> ElectronAPI

' 组合关系
MainEntry *-- ElectronMain
ElectronMain *-- MainWindow
ElectronMain *-- UpdaterWindow
ElectronMain *-- Downloader
BaseWindow *-- BrowserWindow
BaseWindow *-- MenuBuilder
ElectronClient *-- DownloadTask
VueApp *-- ElectronClient

' 依赖关系
VueApp ..> MainProcessAPI
ElectronClient ..> MainProcessAPI
PreloadScript ..> ElectronMain
PreloadBridge ..> ElectronMain
DownloadTask ..> ElectronClient

' 入口点标记
note top of MainEntry : 主进程入口点\n(main/index.ts)
note top of VueApp : 渲染进程入口点\n(App.vue)
note top of PreloadScript : 预加载脚本入口点\n(preload/index.ts)

@enduml
