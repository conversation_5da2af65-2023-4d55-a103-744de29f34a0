@startuml
!theme plain
skinparam backgroundColor white
skinparam classBackgroundColor white
skinparam classBorderColor black
skinparam arrowColor black

' 定义包和进程边界
package "渲染进程 (Renderer Process)" as RendererProcess {
    class ElectronClient {
        -api: ElectronAPI
        -eventUnsubscribers: Map<string, Function>
        +constructor()
        +invoke(method, args)
        +onMainProcess(event, callback)
        +emitToMainProcess(event, data)
        +startDownloadTask(options): DownloadTask
        +openMainWindow(options)
        +cleanup()
    }

    class DownloadTask {
        +taskId: string
        +client: ElectronClient
        +onProgress(callback)
        +onComplete(callback)
        +onError(callback)
        +abort()
    }

    interface ElectronAPI {
        +invoke(method, args)
        +on(event, listener)
        +off(event, listener)
        +emit(event, data)
        +once(event, listener)
    }
}

package "预加载脚本 (Preload Script)" as PreloadProcess {
    class PreloadBridge {
        -eventListeners: Map<string, Set<Function>>
        +createElectronBridge(): ElectronAPI
        +setupIPC()
    }
}

package "主进程 (Main Process)" as MainProcess {
    class ElectronMain {
        -apiHandlers: Partial<MainProcessAPI>
        -eventListeners: Map<string, Set<Function>>
        -customWindows: Map<string, BaseWindow>
        -updateTask: Map<string, Downloader>
        -settings: AppSettings
        +constructor()
        +setupIPC()
        +registerAPI(handlers)
        +broadcast(event, data)
        +onRendererEvent(event, listener)
        +openMainWindow(options)
        +startDownloadTask(options)
    }

    class Downloader {
        -packageName: string
        -cachePath: string
        -url: string
        -downloadTask: CancelableRequest
        +taskId: string
        +constructor(options)
        +start()
        +cancel()
        +checkMD5(filePath, md5)
    }

    class UpdaterWindow {
        -window: BaseWindow
        +constructor()
        +start()
        +close()
        +createUpdateWindow()
        +setupWindowEventListeners()
    }

    class MainWindow {
        -window: BaseWindow
        +constructor()
        +getWindowConfig()
        +getLoadOptions()
        +create()
    }

    class BaseWindow {
        +webContents: WebContents
        +constructor()
        +loadURL(url)
        +close()
        +isDestroyed()
    }
}

' 基础类
class EventEmitter <<Node.js Native>> {
    +on(event, listener)
    +emit(event, data)
    +removeAllListeners()
}

' 继承关系
ElectronClient --|> EventEmitter
ElectronMain --|> EventEmitter
BaseWindow --|> EventEmitter
UpdaterWindow --|> BaseWindow
MainWindow --|> BaseWindow
Downloader --|> EventEmitter

' 组合关系
ElectronClient *-- ElectronAPI : uses
ElectronClient *-- DownloadTask : creates
ElectronMain *-- Downloader : manages
ElectronMain *-- BaseWindow : manages

' 实现关系
PreloadBridge ..|> ElectronAPI : implements

' 依赖关系
ElectronAPI ..> PreloadBridge : implemented by
PreloadBridge ..> ElectronMain : IPC communication
DownloadTask ..> ElectronClient : callbacks to

' 颜色定义
skinparam package {
    BackgroundColor<<RendererProcess>> #e1f5fe
    BorderColor<<RendererProcess>> #01579b
    BackgroundColor<<MainProcess>> #f3e5f5
    BorderColor<<MainProcess>> #4a148c
    BackgroundColor<<PreloadProcess>> #fff3e0
    BorderColor<<PreloadProcess>> #e65100
}
@enduml
