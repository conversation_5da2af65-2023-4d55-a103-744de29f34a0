# getNovaVersion 调用流程分析

## 概述

本文档详细分析了 `App.vue` 中 `getNovaVersion` 函数的完整调用流程，包括版本检查、下载更新和窗口管理等核心功能。

## 流程图

PlantUML 时序图文件：`getNovaVersion-sequence-diagram.puml`

## 主要组件

### 1. App.vue (前端界面层)
- **职责**：用户交互、状态管理、流程控制
- **关键方法**：
  - `getNovaVersion()`: 主入口函数
  - `doUpdateBundle()`: 处理更新下载
  - `updateComplete()`: 完成后处理
  - `setStatus()`: 状态更新

### 2. ElectronClient (渲染进程通信层)
- **职责**：封装 IPC 通信、事件管理
- **关键方法**：
  - `isUpdateAvailable()`: 检查更新
  - `startDownloadTask()`: 启动下载任务
  - `openMainWindow()`: 打开主窗口

### 3. ElectronMain (主进程服务层)
- **职责**：业务逻辑处理、系统资源管理
- **关键方法**：
  - `isUpdateAvailable()`: 版本比较逻辑
  - `startDownloadTask()`: 下载任务管理
  - `openMainWindow()`: 窗口创建

### 4. PackageManager (包管理层)
- **职责**：版本管理、文件操作
- **关键方法**：
  - `getLocalPackageVersion()`: 获取本地版本
  - `getServerPackageVersion()`: 获取远程版本

### 5. Downloader (下载器)
- **职责**：文件下载、进度监控
- **特性**：
  - 支持进度回调
  - 支持任务取消
  - 支持 MD5 校验

## 详细流程

### 阶段1：初始化和触发
```javascript
// 用户点击按钮触发
const getNovaVersion = () => {
    const url = 'https://probe-1318590712.cos.ap-beijing.myqcloud.com/pc-nova/bundles'
    let request = {
        url: `${url}/nova_ui.json`,
        pack: 'main-ui'
    }
    // ...
}
```

### 阶段2：版本检查
1. **本地版本获取**：从文件系统读取当前安装的版本信息
2. **远程版本获取**：HTTP 请求获取服务器最新版本信息
3. **版本比较**：比较本地和远程版本，判断是否需要更新

### 阶段3：更新处理
#### 3.1 有更新可用
```javascript
if (result.hasUpdate) {
    doUpdateBundle(result, url)
}
```

#### 3.2 下载流程
1. **任务创建**：生成唯一 taskId，创建下载任务
2. **进度监控**：通过事件机制实时更新下载进度
3. **完成处理**：下载完成后触发后续流程

#### 3.3 无更新
```javascript
else {
    setStatus(UPDATE.LASTEST)
    updateComplete()
}
```

### 阶段4：窗口管理
- 无论是否有更新，最终都会调用 `openMainWindow()` 打开主应用窗口

## 关键数据结构

### IsUpdateAvailableOptions
```typescript
interface IsUpdateAvailableOptions {
    url: string              // 远程版本文件URL
    pack: string            // 包名
    checkVersionOnly?: boolean
}
```

### IsUpdateAvailableResponse
```typescript
interface IsUpdateAvailableResponse {
    hasUpdate: boolean      // 是否有更新
    localVersion?: string   // 本地版本
    serverVersion?: string  // 服务器版本
    md5?: string           // 文件MD5
    url?: string           // 下载URL
    error?: string         // 错误信息
}
```

### DownloadTaskOptions
```typescript
interface DownloadTaskOptions {
    pack: string           // 包名
    url: string           // 下载URL
    md5?: string          // MD5校验
    version: string       // 版本号
    autoUnzip?: boolean   // 自动解压
    checksum?: boolean    // 校验和验证
    taskId?: string       // 任务ID
}
```

## 事件驱动机制

### 下载事件
- `progress`: 下载进度更新
- `complete`: 下载完成
- `error`: 下载错误

### IPC 通信
- 渲染进程通过 `electronClient` 与主进程通信
- 主进程通过 `webContents.send()` 向渲染进程发送事件
- 支持双向异步通信

## 错误处理

### 网络错误
- HTTP 请求失败
- 下载中断
- 服务器不可达

### 文件系统错误
- 本地版本文件不存在
- 写入权限不足
- 磁盘空间不足

### 版本校验错误
- MD5 校验失败
- 版本格式不正确
- 解压失败

## 状态管理

使用 `UPDATE` 常量定义各种状态：
- `UPDATE.CHECKING`: 检查中
- `UPDATE.AVAILABLE`: 发现更新
- `UPDATE.DOWNLOADING_UI`: 下载中
- `UPDATE.LASTEST`: 已是最新
- `UPDATE.ERROR`: 更新错误

## 优化建议

1. **错误重试机制**：网络请求失败时自动重试
2. **缓存策略**：缓存版本信息减少网络请求
3. **增量更新**：支持差分更新减少下载量
4. **并发控制**：限制同时下载任务数量
5. **用户体验**：提供更详细的进度信息和错误提示
