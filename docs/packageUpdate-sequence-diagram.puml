@startuml getNovaVersion调用流程时序图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

title getNovaVersion 完整调用流程

actor "用户界面" as User
participant "App.vue" as App
participant "ElectronClient" as Client
participant "ElectronMain" as Main
participant "PackageManager" as PM
participant "远程服务器" as Server
participant "Downloader" as DL
participant "文件系统" as FS

note over User, FS : getNovaVersion 完整调用流程

User -> App : 点击"业务层检查更新"按钮
activate App

App -> App : getNovaVersion()
note right of App : 构建请求参数
App -> App : 创建request对象\n{url: "nova_ui.json", pack: "main-ui"}

App -> Client : electronClient.isUpdateAvailable(request)
activate Client

Client -> Client : invoke('isUpdateAvailable', options)
Client -> Main : IPC调用 isUpdateAvailable
activate Main

Main -> PM : getLocalPackageVersion(pack)
activate PM
PM -> FS : 读取本地版本信息
activate FS
FS --> PM : 返回本地版本
deactivate FS
PM --> Main : 返回本地版本信息
deactivate PM

Main -> PM : getServerPackageVersion(url)
activate PM
PM -> Server : HTTP请求获取远程版本
activate Server
Server --> PM : 返回远程版本信息
deactivate Server
PM --> Main : 返回远程版本信息
deactivate PM

Main -> Main : 比较版本差异
Main --> Client : 返回IsUpdateAvailableResponse
deactivate Main
Client --> App : 返回检查结果
deactivate Client

alt 有更新可用
    App -> App : doUpdateBundle(result, url)
    App -> App : setStatus(UPDATE.DOWNLOADING_UI)
    
    note right of App : 构建下载任务参数
    App -> App : 创建DownloadTaskOptions\n{pack, url, md5, version, autoUnzip, checksum}
    
    App -> Client : electronClient.startDownloadTask(request)
    activate Client
    Client -> Client : 生成taskId (UUID)
    Client -> Client : 创建DownloadTask实例
    Client -> Main : IPC调用 startDownloadTask
    activate Main
    
    Main -> DL : 创建Downloader实例
    activate DL
    Main -> DL : task.start()
    DL -> Server : 开始下载文件
    activate Server
    
    loop 下载进度更新
        DL -> Main : 发送progress事件
        Main -> Client : 通过IPC发送download-event
        Client -> App : 触发task.on('progress')
        App -> App : 更新下载进度
    end
    
    Server --> DL : 下载数据流
    deactivate Server
    DL -> Main : 下载完成事件
    deactivate DL
    Main -> Client : 发送complete事件
    deactivate Main
    Client -> App : 触发task.on('complete')
    deactivate Client
    App -> App : updateComplete()
    
    App -> Client : electronClient.openMainWindow(options)
    activate Client
    Client -> Main : IPC调用 openMainWindow
    activate Main
    Main -> Main : 创建主窗口
    deactivate Main
    deactivate Client
    
else 已是最新版本
    App -> App : setStatus(UPDATE.LASTEST)
    App -> App : updateComplete()
    App -> Client : electronClient.openMainWindow(options)
    activate Client
    Client -> Main : IPC调用 openMainWindow
    activate Main
    Main -> Main : 创建主窗口
    deactivate Main
    deactivate Client
end

deactivate App

note over User, FS : 流程结束

@enduml
